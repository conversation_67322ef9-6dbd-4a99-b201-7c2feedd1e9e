{"version": 3, "sources": ["../../three/examples/jsm/controls/OrbitControls.js"], "sourcesContent": ["import {\n\tEventDispatcher,\n\t<PERSON>O<PERSON><PERSON>,\n\tQuaternion,\n\tSpherical,\n\tTOUCH,\n\tVector2,\n\tVector3\n} from 'three';\n\n// OrbitControls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst _changeEvent = { type: 'change' };\nconst _startEvent = { type: 'start' };\nconst _endEvent = { type: 'end' };\n\nclass OrbitControls extends EventDispatcher {\n\n\tconstructor( object, domElement ) {\n\n\t\tsuper();\n\n\t\tthis.object = object;\n\t\tthis.domElement = domElement;\n\t\tthis.domElement.style.touchAction = 'none'; // disable touch scroll\n\n\t\t// Set to false to disable this control\n\t\tthis.enabled = true;\n\n\t\t// \"target\" sets the location of focus, where the object orbits around\n\t\tthis.target = new Vector3();\n\n\t\t// How far you can dolly in and out ( PerspectiveCamera only )\n\t\tthis.minDistance = 0;\n\t\tthis.maxDistance = Infinity;\n\n\t\t// How far you can zoom in and out ( OrthographicCamera only )\n\t\tthis.minZoom = 0;\n\t\tthis.maxZoom = Infinity;\n\n\t\t// How far you can orbit vertically, upper and lower limits.\n\t\t// Range is 0 to Math.PI radians.\n\t\tthis.minPolarAngle = 0; // radians\n\t\tthis.maxPolarAngle = Math.PI; // radians\n\n\t\t// How far you can orbit horizontally, upper and lower limits.\n\t\t// If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n\t\tthis.minAzimuthAngle = - Infinity; // radians\n\t\tthis.maxAzimuthAngle = Infinity; // radians\n\n\t\t// Set to true to enable damping (inertia)\n\t\t// If damping is enabled, you must call controls.update() in your animation loop\n\t\tthis.enableDamping = false;\n\t\tthis.dampingFactor = 0.05;\n\n\t\t// This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n\t\t// Set to false to disable zooming\n\t\tthis.enableZoom = true;\n\t\tthis.zoomSpeed = 1.0;\n\n\t\t// Set to false to disable rotating\n\t\tthis.enableRotate = true;\n\t\tthis.rotateSpeed = 1.0;\n\n\t\t// Set to false to disable panning\n\t\tthis.enablePan = true;\n\t\tthis.panSpeed = 1.0;\n\t\tthis.screenSpacePanning = true; // if false, pan orthogonal to world-space direction camera.up\n\t\tthis.keyPanSpeed = 7.0;\t// pixels moved per arrow key push\n\n\t\t// Set to true to automatically rotate around the target\n\t\t// If auto-rotate is enabled, you must call controls.update() in your animation loop\n\t\tthis.autoRotate = false;\n\t\tthis.autoRotateSpeed = 2.0; // 30 seconds per orbit when fps is 60\n\n\t\t// The four arrow keys\n\t\tthis.keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' };\n\n\t\t// Mouse buttons\n\t\tthis.mouseButtons = { LEFT: MOUSE.ROTATE, MIDDLE: MOUSE.DOLLY, RIGHT: MOUSE.PAN };\n\n\t\t// Touch fingers\n\t\tthis.touches = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN };\n\n\t\t// for reset\n\t\tthis.target0 = this.target.clone();\n\t\tthis.position0 = this.object.position.clone();\n\t\tthis.zoom0 = this.object.zoom;\n\n\t\t// the target DOM element for key events\n\t\tthis._domElementKeyEvents = null;\n\n\t\t//\n\t\t// public methods\n\t\t//\n\n\t\tthis.getPolarAngle = function () {\n\n\t\t\treturn spherical.phi;\n\n\t\t};\n\n\t\tthis.getAzimuthalAngle = function () {\n\n\t\t\treturn spherical.theta;\n\n\t\t};\n\n\t\tthis.getDistance = function () {\n\n\t\t\treturn this.object.position.distanceTo( this.target );\n\n\t\t};\n\n\t\tthis.listenToKeyEvents = function ( domElement ) {\n\n\t\t\tdomElement.addEventListener( 'keydown', onKeyDown );\n\t\t\tthis._domElementKeyEvents = domElement;\n\n\t\t};\n\n\t\tthis.stopListenToKeyEvents = function () {\n\n\t\t\tthis._domElementKeyEvents.removeEventListener( 'keydown', onKeyDown );\n\t\t\tthis._domElementKeyEvents = null;\n\n\t\t};\n\n\t\tthis.saveState = function () {\n\n\t\t\tscope.target0.copy( scope.target );\n\t\t\tscope.position0.copy( scope.object.position );\n\t\t\tscope.zoom0 = scope.object.zoom;\n\n\t\t};\n\n\t\tthis.reset = function () {\n\n\t\t\tscope.target.copy( scope.target0 );\n\t\t\tscope.object.position.copy( scope.position0 );\n\t\t\tscope.object.zoom = scope.zoom0;\n\n\t\t\tscope.object.updateProjectionMatrix();\n\t\t\tscope.dispatchEvent( _changeEvent );\n\n\t\t\tscope.update();\n\n\t\t\tstate = STATE.NONE;\n\n\t\t};\n\n\t\t// this method is exposed, but perhaps it would be better if we can make it private...\n\t\tthis.update = function () {\n\n\t\t\tconst offset = new Vector3();\n\n\t\t\t// so camera.up is the orbit axis\n\t\t\tconst quat = new Quaternion().setFromUnitVectors( object.up, new Vector3( 0, 1, 0 ) );\n\t\t\tconst quatInverse = quat.clone().invert();\n\n\t\t\tconst lastPosition = new Vector3();\n\t\t\tconst lastQuaternion = new Quaternion();\n\n\t\t\tconst twoPI = 2 * Math.PI;\n\n\t\t\treturn function update() {\n\n\t\t\t\tconst position = scope.object.position;\n\n\t\t\t\toffset.copy( position ).sub( scope.target );\n\n\t\t\t\t// rotate offset to \"y-axis-is-up\" space\n\t\t\t\toffset.applyQuaternion( quat );\n\n\t\t\t\t// angle from z-axis around y-axis\n\t\t\t\tspherical.setFromVector3( offset );\n\n\t\t\t\tif ( scope.autoRotate && state === STATE.NONE ) {\n\n\t\t\t\t\trotateLeft( getAutoRotationAngle() );\n\n\t\t\t\t}\n\n\t\t\t\tif ( scope.enableDamping ) {\n\n\t\t\t\t\tspherical.theta += sphericalDelta.theta * scope.dampingFactor;\n\t\t\t\t\tspherical.phi += sphericalDelta.phi * scope.dampingFactor;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tspherical.theta += sphericalDelta.theta;\n\t\t\t\t\tspherical.phi += sphericalDelta.phi;\n\n\t\t\t\t}\n\n\t\t\t\t// restrict theta to be between desired limits\n\n\t\t\t\tlet min = scope.minAzimuthAngle;\n\t\t\t\tlet max = scope.maxAzimuthAngle;\n\n\t\t\t\tif ( isFinite( min ) && isFinite( max ) ) {\n\n\t\t\t\t\tif ( min < - Math.PI ) min += twoPI; else if ( min > Math.PI ) min -= twoPI;\n\n\t\t\t\t\tif ( max < - Math.PI ) max += twoPI; else if ( max > Math.PI ) max -= twoPI;\n\n\t\t\t\t\tif ( min <= max ) {\n\n\t\t\t\t\t\tspherical.theta = Math.max( min, Math.min( max, spherical.theta ) );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tspherical.theta = ( spherical.theta > ( min + max ) / 2 ) ?\n\t\t\t\t\t\t\tMath.max( min, spherical.theta ) :\n\t\t\t\t\t\t\tMath.min( max, spherical.theta );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// restrict phi to be between desired limits\n\t\t\t\tspherical.phi = Math.max( scope.minPolarAngle, Math.min( scope.maxPolarAngle, spherical.phi ) );\n\n\t\t\t\tspherical.makeSafe();\n\n\n\t\t\t\tspherical.radius *= scale;\n\n\t\t\t\t// restrict radius to be between desired limits\n\t\t\t\tspherical.radius = Math.max( scope.minDistance, Math.min( scope.maxDistance, spherical.radius ) );\n\n\t\t\t\t// move target to panned location\n\n\t\t\t\tif ( scope.enableDamping === true ) {\n\n\t\t\t\t\tscope.target.addScaledVector( panOffset, scope.dampingFactor );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tscope.target.add( panOffset );\n\n\t\t\t\t}\n\n\t\t\t\toffset.setFromSpherical( spherical );\n\n\t\t\t\t// rotate offset back to \"camera-up-vector-is-up\" space\n\t\t\t\toffset.applyQuaternion( quatInverse );\n\n\t\t\t\tposition.copy( scope.target ).add( offset );\n\n\t\t\t\tscope.object.lookAt( scope.target );\n\n\t\t\t\tif ( scope.enableDamping === true ) {\n\n\t\t\t\t\tsphericalDelta.theta *= ( 1 - scope.dampingFactor );\n\t\t\t\t\tsphericalDelta.phi *= ( 1 - scope.dampingFactor );\n\n\t\t\t\t\tpanOffset.multiplyScalar( 1 - scope.dampingFactor );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tsphericalDelta.set( 0, 0, 0 );\n\n\t\t\t\t\tpanOffset.set( 0, 0, 0 );\n\n\t\t\t\t}\n\n\t\t\t\tscale = 1;\n\n\t\t\t\t// update condition is:\n\t\t\t\t// min(camera displacement, camera rotation in radians)^2 > EPS\n\t\t\t\t// using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n\t\t\t\tif ( zoomChanged ||\n\t\t\t\t\tlastPosition.distanceToSquared( scope.object.position ) > EPS ||\n\t\t\t\t\t8 * ( 1 - lastQuaternion.dot( scope.object.quaternion ) ) > EPS ) {\n\n\t\t\t\t\tscope.dispatchEvent( _changeEvent );\n\n\t\t\t\t\tlastPosition.copy( scope.object.position );\n\t\t\t\t\tlastQuaternion.copy( scope.object.quaternion );\n\t\t\t\t\tzoomChanged = false;\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t};\n\n\t\t}();\n\n\t\tthis.dispose = function () {\n\n\t\t\tscope.domElement.removeEventListener( 'contextmenu', onContextMenu );\n\n\t\t\tscope.domElement.removeEventListener( 'pointerdown', onPointerDown );\n\t\t\tscope.domElement.removeEventListener( 'pointercancel', onPointerUp );\n\t\t\tscope.domElement.removeEventListener( 'wheel', onMouseWheel );\n\n\t\t\tscope.domElement.removeEventListener( 'pointermove', onPointerMove );\n\t\t\tscope.domElement.removeEventListener( 'pointerup', onPointerUp );\n\n\n\t\t\tif ( scope._domElementKeyEvents !== null ) {\n\n\t\t\t\tscope._domElementKeyEvents.removeEventListener( 'keydown', onKeyDown );\n\t\t\t\tscope._domElementKeyEvents = null;\n\n\t\t\t}\n\n\t\t\t//scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n\n\t\t};\n\n\t\t//\n\t\t// internals\n\t\t//\n\n\t\tconst scope = this;\n\n\t\tconst STATE = {\n\t\t\tNONE: - 1,\n\t\t\tROTATE: 0,\n\t\t\tDOLLY: 1,\n\t\t\tPAN: 2,\n\t\t\tTOUCH_ROTATE: 3,\n\t\t\tTOUCH_PAN: 4,\n\t\t\tTOUCH_DOLLY_PAN: 5,\n\t\t\tTOUCH_DOLLY_ROTATE: 6\n\t\t};\n\n\t\tlet state = STATE.NONE;\n\n\t\tconst EPS = 0.000001;\n\n\t\t// current position in spherical coordinates\n\t\tconst spherical = new Spherical();\n\t\tconst sphericalDelta = new Spherical();\n\n\t\tlet scale = 1;\n\t\tconst panOffset = new Vector3();\n\t\tlet zoomChanged = false;\n\n\t\tconst rotateStart = new Vector2();\n\t\tconst rotateEnd = new Vector2();\n\t\tconst rotateDelta = new Vector2();\n\n\t\tconst panStart = new Vector2();\n\t\tconst panEnd = new Vector2();\n\t\tconst panDelta = new Vector2();\n\n\t\tconst dollyStart = new Vector2();\n\t\tconst dollyEnd = new Vector2();\n\t\tconst dollyDelta = new Vector2();\n\n\t\tconst pointers = [];\n\t\tconst pointerPositions = {};\n\n\t\tfunction getAutoRotationAngle() {\n\n\t\t\treturn 2 * Math.PI / 60 / 60 * scope.autoRotateSpeed;\n\n\t\t}\n\n\t\tfunction getZoomScale() {\n\n\t\t\treturn Math.pow( 0.95, scope.zoomSpeed );\n\n\t\t}\n\n\t\tfunction rotateLeft( angle ) {\n\n\t\t\tsphericalDelta.theta -= angle;\n\n\t\t}\n\n\t\tfunction rotateUp( angle ) {\n\n\t\t\tsphericalDelta.phi -= angle;\n\n\t\t}\n\n\t\tconst panLeft = function () {\n\n\t\t\tconst v = new Vector3();\n\n\t\t\treturn function panLeft( distance, objectMatrix ) {\n\n\t\t\t\tv.setFromMatrixColumn( objectMatrix, 0 ); // get X column of objectMatrix\n\t\t\t\tv.multiplyScalar( - distance );\n\n\t\t\t\tpanOffset.add( v );\n\n\t\t\t};\n\n\t\t}();\n\n\t\tconst panUp = function () {\n\n\t\t\tconst v = new Vector3();\n\n\t\t\treturn function panUp( distance, objectMatrix ) {\n\n\t\t\t\tif ( scope.screenSpacePanning === true ) {\n\n\t\t\t\t\tv.setFromMatrixColumn( objectMatrix, 1 );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tv.setFromMatrixColumn( objectMatrix, 0 );\n\t\t\t\t\tv.crossVectors( scope.object.up, v );\n\n\t\t\t\t}\n\n\t\t\t\tv.multiplyScalar( distance );\n\n\t\t\t\tpanOffset.add( v );\n\n\t\t\t};\n\n\t\t}();\n\n\t\t// deltaX and deltaY are in pixels; right and down are positive\n\t\tconst pan = function () {\n\n\t\t\tconst offset = new Vector3();\n\n\t\t\treturn function pan( deltaX, deltaY ) {\n\n\t\t\t\tconst element = scope.domElement;\n\n\t\t\t\tif ( scope.object.isPerspectiveCamera ) {\n\n\t\t\t\t\t// perspective\n\t\t\t\t\tconst position = scope.object.position;\n\t\t\t\t\toffset.copy( position ).sub( scope.target );\n\t\t\t\t\tlet targetDistance = offset.length();\n\n\t\t\t\t\t// half of the fov is center to top of screen\n\t\t\t\t\ttargetDistance *= Math.tan( ( scope.object.fov / 2 ) * Math.PI / 180.0 );\n\n\t\t\t\t\t// we use only clientHeight here so aspect ratio does not distort speed\n\t\t\t\t\tpanLeft( 2 * deltaX * targetDistance / element.clientHeight, scope.object.matrix );\n\t\t\t\t\tpanUp( 2 * deltaY * targetDistance / element.clientHeight, scope.object.matrix );\n\n\t\t\t\t} else if ( scope.object.isOrthographicCamera ) {\n\n\t\t\t\t\t// orthographic\n\t\t\t\t\tpanLeft( deltaX * ( scope.object.right - scope.object.left ) / scope.object.zoom / element.clientWidth, scope.object.matrix );\n\t\t\t\t\tpanUp( deltaY * ( scope.object.top - scope.object.bottom ) / scope.object.zoom / element.clientHeight, scope.object.matrix );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// camera neither orthographic nor perspective\n\t\t\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.' );\n\t\t\t\t\tscope.enablePan = false;\n\n\t\t\t\t}\n\n\t\t\t};\n\n\t\t}();\n\n\t\tfunction dollyOut( dollyScale ) {\n\n\t\t\tif ( scope.object.isPerspectiveCamera ) {\n\n\t\t\t\tscale /= dollyScale;\n\n\t\t\t} else if ( scope.object.isOrthographicCamera ) {\n\n\t\t\t\tscope.object.zoom = Math.max( scope.minZoom, Math.min( scope.maxZoom, scope.object.zoom * dollyScale ) );\n\t\t\t\tscope.object.updateProjectionMatrix();\n\t\t\t\tzoomChanged = true;\n\n\t\t\t} else {\n\n\t\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.' );\n\t\t\t\tscope.enableZoom = false;\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction dollyIn( dollyScale ) {\n\n\t\t\tif ( scope.object.isPerspectiveCamera ) {\n\n\t\t\t\tscale *= dollyScale;\n\n\t\t\t} else if ( scope.object.isOrthographicCamera ) {\n\n\t\t\t\tscope.object.zoom = Math.max( scope.minZoom, Math.min( scope.maxZoom, scope.object.zoom / dollyScale ) );\n\t\t\t\tscope.object.updateProjectionMatrix();\n\t\t\t\tzoomChanged = true;\n\n\t\t\t} else {\n\n\t\t\t\tconsole.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.' );\n\t\t\t\tscope.enableZoom = false;\n\n\t\t\t}\n\n\t\t}\n\n\t\t//\n\t\t// event callbacks - update the object state\n\t\t//\n\n\t\tfunction handleMouseDownRotate( event ) {\n\n\t\t\trotateStart.set( event.clientX, event.clientY );\n\n\t\t}\n\n\t\tfunction handleMouseDownDolly( event ) {\n\n\t\t\tdollyStart.set( event.clientX, event.clientY );\n\n\t\t}\n\n\t\tfunction handleMouseDownPan( event ) {\n\n\t\t\tpanStart.set( event.clientX, event.clientY );\n\n\t\t}\n\n\t\tfunction handleMouseMoveRotate( event ) {\n\n\t\t\trotateEnd.set( event.clientX, event.clientY );\n\n\t\t\trotateDelta.subVectors( rotateEnd, rotateStart ).multiplyScalar( scope.rotateSpeed );\n\n\t\t\tconst element = scope.domElement;\n\n\t\t\trotateLeft( 2 * Math.PI * rotateDelta.x / element.clientHeight ); // yes, height\n\n\t\t\trotateUp( 2 * Math.PI * rotateDelta.y / element.clientHeight );\n\n\t\t\trotateStart.copy( rotateEnd );\n\n\t\t\tscope.update();\n\n\t\t}\n\n\t\tfunction handleMouseMoveDolly( event ) {\n\n\t\t\tdollyEnd.set( event.clientX, event.clientY );\n\n\t\t\tdollyDelta.subVectors( dollyEnd, dollyStart );\n\n\t\t\tif ( dollyDelta.y > 0 ) {\n\n\t\t\t\tdollyOut( getZoomScale() );\n\n\t\t\t} else if ( dollyDelta.y < 0 ) {\n\n\t\t\t\tdollyIn( getZoomScale() );\n\n\t\t\t}\n\n\t\t\tdollyStart.copy( dollyEnd );\n\n\t\t\tscope.update();\n\n\t\t}\n\n\t\tfunction handleMouseMovePan( event ) {\n\n\t\t\tpanEnd.set( event.clientX, event.clientY );\n\n\t\t\tpanDelta.subVectors( panEnd, panStart ).multiplyScalar( scope.panSpeed );\n\n\t\t\tpan( panDelta.x, panDelta.y );\n\n\t\t\tpanStart.copy( panEnd );\n\n\t\t\tscope.update();\n\n\t\t}\n\n\t\tfunction handleMouseWheel( event ) {\n\n\t\t\tif ( event.deltaY < 0 ) {\n\n\t\t\t\tdollyIn( getZoomScale() );\n\n\t\t\t} else if ( event.deltaY > 0 ) {\n\n\t\t\t\tdollyOut( getZoomScale() );\n\n\t\t\t}\n\n\t\t\tscope.update();\n\n\t\t}\n\n\t\tfunction handleKeyDown( event ) {\n\n\t\t\tlet needsUpdate = false;\n\n\t\t\tswitch ( event.code ) {\n\n\t\t\t\tcase scope.keys.UP:\n\n\t\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\t\trotateUp( 2 * Math.PI * scope.rotateSpeed / scope.domElement.clientHeight );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tpan( 0, scope.keyPanSpeed );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tneedsUpdate = true;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase scope.keys.BOTTOM:\n\n\t\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\t\trotateUp( - 2 * Math.PI * scope.rotateSpeed / scope.domElement.clientHeight );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tpan( 0, - scope.keyPanSpeed );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tneedsUpdate = true;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase scope.keys.LEFT:\n\n\t\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\t\trotateLeft( 2 * Math.PI * scope.rotateSpeed / scope.domElement.clientHeight );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tpan( scope.keyPanSpeed, 0 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tneedsUpdate = true;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase scope.keys.RIGHT:\n\n\t\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\t\trotateLeft( - 2 * Math.PI * scope.rotateSpeed / scope.domElement.clientHeight );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tpan( - scope.keyPanSpeed, 0 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tneedsUpdate = true;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tif ( needsUpdate ) {\n\n\t\t\t\t// prevent the browser from scrolling on cursor keys\n\t\t\t\tevent.preventDefault();\n\n\t\t\t\tscope.update();\n\n\t\t\t}\n\n\n\t\t}\n\n\t\tfunction handleTouchStartRotate() {\n\n\t\t\tif ( pointers.length === 1 ) {\n\n\t\t\t\trotateStart.set( pointers[ 0 ].pageX, pointers[ 0 ].pageY );\n\n\t\t\t} else {\n\n\t\t\t\tconst x = 0.5 * ( pointers[ 0 ].pageX + pointers[ 1 ].pageX );\n\t\t\t\tconst y = 0.5 * ( pointers[ 0 ].pageY + pointers[ 1 ].pageY );\n\n\t\t\t\trotateStart.set( x, y );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction handleTouchStartPan() {\n\n\t\t\tif ( pointers.length === 1 ) {\n\n\t\t\t\tpanStart.set( pointers[ 0 ].pageX, pointers[ 0 ].pageY );\n\n\t\t\t} else {\n\n\t\t\t\tconst x = 0.5 * ( pointers[ 0 ].pageX + pointers[ 1 ].pageX );\n\t\t\t\tconst y = 0.5 * ( pointers[ 0 ].pageY + pointers[ 1 ].pageY );\n\n\t\t\t\tpanStart.set( x, y );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction handleTouchStartDolly() {\n\n\t\t\tconst dx = pointers[ 0 ].pageX - pointers[ 1 ].pageX;\n\t\t\tconst dy = pointers[ 0 ].pageY - pointers[ 1 ].pageY;\n\n\t\t\tconst distance = Math.sqrt( dx * dx + dy * dy );\n\n\t\t\tdollyStart.set( 0, distance );\n\n\t\t}\n\n\t\tfunction handleTouchStartDollyPan() {\n\n\t\t\tif ( scope.enableZoom ) handleTouchStartDolly();\n\n\t\t\tif ( scope.enablePan ) handleTouchStartPan();\n\n\t\t}\n\n\t\tfunction handleTouchStartDollyRotate() {\n\n\t\t\tif ( scope.enableZoom ) handleTouchStartDolly();\n\n\t\t\tif ( scope.enableRotate ) handleTouchStartRotate();\n\n\t\t}\n\n\t\tfunction handleTouchMoveRotate( event ) {\n\n\t\t\tif ( pointers.length == 1 ) {\n\n\t\t\t\trotateEnd.set( event.pageX, event.pageY );\n\n\t\t\t} else {\n\n\t\t\t\tconst position = getSecondPointerPosition( event );\n\n\t\t\t\tconst x = 0.5 * ( event.pageX + position.x );\n\t\t\t\tconst y = 0.5 * ( event.pageY + position.y );\n\n\t\t\t\trotateEnd.set( x, y );\n\n\t\t\t}\n\n\t\t\trotateDelta.subVectors( rotateEnd, rotateStart ).multiplyScalar( scope.rotateSpeed );\n\n\t\t\tconst element = scope.domElement;\n\n\t\t\trotateLeft( 2 * Math.PI * rotateDelta.x / element.clientHeight ); // yes, height\n\n\t\t\trotateUp( 2 * Math.PI * rotateDelta.y / element.clientHeight );\n\n\t\t\trotateStart.copy( rotateEnd );\n\n\t\t}\n\n\t\tfunction handleTouchMovePan( event ) {\n\n\t\t\tif ( pointers.length === 1 ) {\n\n\t\t\t\tpanEnd.set( event.pageX, event.pageY );\n\n\t\t\t} else {\n\n\t\t\t\tconst position = getSecondPointerPosition( event );\n\n\t\t\t\tconst x = 0.5 * ( event.pageX + position.x );\n\t\t\t\tconst y = 0.5 * ( event.pageY + position.y );\n\n\t\t\t\tpanEnd.set( x, y );\n\n\t\t\t}\n\n\t\t\tpanDelta.subVectors( panEnd, panStart ).multiplyScalar( scope.panSpeed );\n\n\t\t\tpan( panDelta.x, panDelta.y );\n\n\t\t\tpanStart.copy( panEnd );\n\n\t\t}\n\n\t\tfunction handleTouchMoveDolly( event ) {\n\n\t\t\tconst position = getSecondPointerPosition( event );\n\n\t\t\tconst dx = event.pageX - position.x;\n\t\t\tconst dy = event.pageY - position.y;\n\n\t\t\tconst distance = Math.sqrt( dx * dx + dy * dy );\n\n\t\t\tdollyEnd.set( 0, distance );\n\n\t\t\tdollyDelta.set( 0, Math.pow( dollyEnd.y / dollyStart.y, scope.zoomSpeed ) );\n\n\t\t\tdollyOut( dollyDelta.y );\n\n\t\t\tdollyStart.copy( dollyEnd );\n\n\t\t}\n\n\t\tfunction handleTouchMoveDollyPan( event ) {\n\n\t\t\tif ( scope.enableZoom ) handleTouchMoveDolly( event );\n\n\t\t\tif ( scope.enablePan ) handleTouchMovePan( event );\n\n\t\t}\n\n\t\tfunction handleTouchMoveDollyRotate( event ) {\n\n\t\t\tif ( scope.enableZoom ) handleTouchMoveDolly( event );\n\n\t\t\tif ( scope.enableRotate ) handleTouchMoveRotate( event );\n\n\t\t}\n\n\t\t//\n\t\t// event handlers - FSM: listen for events and reset state\n\t\t//\n\n\t\tfunction onPointerDown( event ) {\n\n\t\t\tif ( scope.enabled === false ) return;\n\n\t\t\tif ( pointers.length === 0 ) {\n\n\t\t\t\tscope.domElement.setPointerCapture( event.pointerId );\n\n\t\t\t\tscope.domElement.addEventListener( 'pointermove', onPointerMove );\n\t\t\t\tscope.domElement.addEventListener( 'pointerup', onPointerUp );\n\n\t\t\t}\n\n\t\t\t//\n\n\t\t\taddPointer( event );\n\n\t\t\tif ( event.pointerType === 'touch' ) {\n\n\t\t\t\tonTouchStart( event );\n\n\t\t\t} else {\n\n\t\t\t\tonMouseDown( event );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction onPointerMove( event ) {\n\n\t\t\tif ( scope.enabled === false ) return;\n\n\t\t\tif ( event.pointerType === 'touch' ) {\n\n\t\t\t\tonTouchMove( event );\n\n\t\t\t} else {\n\n\t\t\t\tonMouseMove( event );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction onPointerUp( event ) {\n\n\t\t\tremovePointer( event );\n\n\t\t\tif ( pointers.length === 0 ) {\n\n\t\t\t\tscope.domElement.releasePointerCapture( event.pointerId );\n\n\t\t\t\tscope.domElement.removeEventListener( 'pointermove', onPointerMove );\n\t\t\t\tscope.domElement.removeEventListener( 'pointerup', onPointerUp );\n\n\t\t\t}\n\n\t\t\tscope.dispatchEvent( _endEvent );\n\n\t\t\tstate = STATE.NONE;\n\n\t\t}\n\n\t\tfunction onMouseDown( event ) {\n\n\t\t\tlet mouseAction;\n\n\t\t\tswitch ( event.button ) {\n\n\t\t\t\tcase 0:\n\n\t\t\t\t\tmouseAction = scope.mouseButtons.LEFT;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 1:\n\n\t\t\t\t\tmouseAction = scope.mouseButtons.MIDDLE;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 2:\n\n\t\t\t\t\tmouseAction = scope.mouseButtons.RIGHT;\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\tmouseAction = - 1;\n\n\t\t\t}\n\n\t\t\tswitch ( mouseAction ) {\n\n\t\t\t\tcase MOUSE.DOLLY:\n\n\t\t\t\t\tif ( scope.enableZoom === false ) return;\n\n\t\t\t\t\thandleMouseDownDolly( event );\n\n\t\t\t\t\tstate = STATE.DOLLY;\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase MOUSE.ROTATE:\n\n\t\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\t\tif ( scope.enablePan === false ) return;\n\n\t\t\t\t\t\thandleMouseDownPan( event );\n\n\t\t\t\t\t\tstate = STATE.PAN;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tif ( scope.enableRotate === false ) return;\n\n\t\t\t\t\t\thandleMouseDownRotate( event );\n\n\t\t\t\t\t\tstate = STATE.ROTATE;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase MOUSE.PAN:\n\n\t\t\t\t\tif ( event.ctrlKey || event.metaKey || event.shiftKey ) {\n\n\t\t\t\t\t\tif ( scope.enableRotate === false ) return;\n\n\t\t\t\t\t\thandleMouseDownRotate( event );\n\n\t\t\t\t\t\tstate = STATE.ROTATE;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tif ( scope.enablePan === false ) return;\n\n\t\t\t\t\t\thandleMouseDownPan( event );\n\n\t\t\t\t\t\tstate = STATE.PAN;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\tstate = STATE.NONE;\n\n\t\t\t}\n\n\t\t\tif ( state !== STATE.NONE ) {\n\n\t\t\t\tscope.dispatchEvent( _startEvent );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction onMouseMove( event ) {\n\n\t\t\tswitch ( state ) {\n\n\t\t\t\tcase STATE.ROTATE:\n\n\t\t\t\t\tif ( scope.enableRotate === false ) return;\n\n\t\t\t\t\thandleMouseMoveRotate( event );\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase STATE.DOLLY:\n\n\t\t\t\t\tif ( scope.enableZoom === false ) return;\n\n\t\t\t\t\thandleMouseMoveDolly( event );\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase STATE.PAN:\n\n\t\t\t\t\tif ( scope.enablePan === false ) return;\n\n\t\t\t\t\thandleMouseMovePan( event );\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction onMouseWheel( event ) {\n\n\t\t\tif ( scope.enabled === false || scope.enableZoom === false || state !== STATE.NONE ) return;\n\n\t\t\tevent.preventDefault();\n\n\t\t\tscope.dispatchEvent( _startEvent );\n\n\t\t\thandleMouseWheel( event );\n\n\t\t\tscope.dispatchEvent( _endEvent );\n\n\t\t}\n\n\t\tfunction onKeyDown( event ) {\n\n\t\t\tif ( scope.enabled === false || scope.enablePan === false ) return;\n\n\t\t\thandleKeyDown( event );\n\n\t\t}\n\n\t\tfunction onTouchStart( event ) {\n\n\t\t\ttrackPointer( event );\n\n\t\t\tswitch ( pointers.length ) {\n\n\t\t\t\tcase 1:\n\n\t\t\t\t\tswitch ( scope.touches.ONE ) {\n\n\t\t\t\t\t\tcase TOUCH.ROTATE:\n\n\t\t\t\t\t\t\tif ( scope.enableRotate === false ) return;\n\n\t\t\t\t\t\t\thandleTouchStartRotate();\n\n\t\t\t\t\t\t\tstate = STATE.TOUCH_ROTATE;\n\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase TOUCH.PAN:\n\n\t\t\t\t\t\t\tif ( scope.enablePan === false ) return;\n\n\t\t\t\t\t\t\thandleTouchStartPan();\n\n\t\t\t\t\t\t\tstate = STATE.TOUCH_PAN;\n\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tdefault:\n\n\t\t\t\t\t\t\tstate = STATE.NONE;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 2:\n\n\t\t\t\t\tswitch ( scope.touches.TWO ) {\n\n\t\t\t\t\t\tcase TOUCH.DOLLY_PAN:\n\n\t\t\t\t\t\t\tif ( scope.enableZoom === false && scope.enablePan === false ) return;\n\n\t\t\t\t\t\t\thandleTouchStartDollyPan();\n\n\t\t\t\t\t\t\tstate = STATE.TOUCH_DOLLY_PAN;\n\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase TOUCH.DOLLY_ROTATE:\n\n\t\t\t\t\t\t\tif ( scope.enableZoom === false && scope.enableRotate === false ) return;\n\n\t\t\t\t\t\t\thandleTouchStartDollyRotate();\n\n\t\t\t\t\t\t\tstate = STATE.TOUCH_DOLLY_ROTATE;\n\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tdefault:\n\n\t\t\t\t\t\t\tstate = STATE.NONE;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\tstate = STATE.NONE;\n\n\t\t\t}\n\n\t\t\tif ( state !== STATE.NONE ) {\n\n\t\t\t\tscope.dispatchEvent( _startEvent );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction onTouchMove( event ) {\n\n\t\t\ttrackPointer( event );\n\n\t\t\tswitch ( state ) {\n\n\t\t\t\tcase STATE.TOUCH_ROTATE:\n\n\t\t\t\t\tif ( scope.enableRotate === false ) return;\n\n\t\t\t\t\thandleTouchMoveRotate( event );\n\n\t\t\t\t\tscope.update();\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase STATE.TOUCH_PAN:\n\n\t\t\t\t\tif ( scope.enablePan === false ) return;\n\n\t\t\t\t\thandleTouchMovePan( event );\n\n\t\t\t\t\tscope.update();\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase STATE.TOUCH_DOLLY_PAN:\n\n\t\t\t\t\tif ( scope.enableZoom === false && scope.enablePan === false ) return;\n\n\t\t\t\t\thandleTouchMoveDollyPan( event );\n\n\t\t\t\t\tscope.update();\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase STATE.TOUCH_DOLLY_ROTATE:\n\n\t\t\t\t\tif ( scope.enableZoom === false && scope.enableRotate === false ) return;\n\n\t\t\t\t\thandleTouchMoveDollyRotate( event );\n\n\t\t\t\t\tscope.update();\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\tstate = STATE.NONE;\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction onContextMenu( event ) {\n\n\t\t\tif ( scope.enabled === false ) return;\n\n\t\t\tevent.preventDefault();\n\n\t\t}\n\n\t\tfunction addPointer( event ) {\n\n\t\t\tpointers.push( event );\n\n\t\t}\n\n\t\tfunction removePointer( event ) {\n\n\t\t\tdelete pointerPositions[ event.pointerId ];\n\n\t\t\tfor ( let i = 0; i < pointers.length; i ++ ) {\n\n\t\t\t\tif ( pointers[ i ].pointerId == event.pointerId ) {\n\n\t\t\t\t\tpointers.splice( i, 1 );\n\t\t\t\t\treturn;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction trackPointer( event ) {\n\n\t\t\tlet position = pointerPositions[ event.pointerId ];\n\n\t\t\tif ( position === undefined ) {\n\n\t\t\t\tposition = new Vector2();\n\t\t\t\tpointerPositions[ event.pointerId ] = position;\n\n\t\t\t}\n\n\t\t\tposition.set( event.pageX, event.pageY );\n\n\t\t}\n\n\t\tfunction getSecondPointerPosition( event ) {\n\n\t\t\tconst pointer = ( event.pointerId === pointers[ 0 ].pointerId ) ? pointers[ 1 ] : pointers[ 0 ];\n\n\t\t\treturn pointerPositions[ pointer.pointerId ];\n\n\t\t}\n\n\t\t//\n\n\t\tscope.domElement.addEventListener( 'contextmenu', onContextMenu );\n\n\t\tscope.domElement.addEventListener( 'pointerdown', onPointerDown );\n\t\tscope.domElement.addEventListener( 'pointercancel', onPointerUp );\n\t\tscope.domElement.addEventListener( 'wheel', onMouseWheel, { passive: false } );\n\n\t\t// force an update at start\n\n\t\tthis.update();\n\n\t}\n\n}\n\nexport { OrbitControls };\n"], "mappings": ";;;;;;;;;;;AAiBA,IAAM,eAAe,EAAE,MAAM,SAAS;AACtC,IAAM,cAAc,EAAE,MAAM,QAAQ;AACpC,IAAM,YAAY,EAAE,MAAM,MAAM;AAEhC,IAAM,gBAAN,cAA4B,gBAAgB;AAAA,EAE3C,YAAa,QAAQ,YAAa;AAEjC,UAAM;AAEN,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW,MAAM,cAAc;AAGpC,SAAK,UAAU;AAGf,SAAK,SAAS,IAAI,QAAQ;AAG1B,SAAK,cAAc;AACnB,SAAK,cAAc;AAGnB,SAAK,UAAU;AACf,SAAK,UAAU;AAIf,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,KAAK;AAI1B,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAIvB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAIrB,SAAK,aAAa;AAClB,SAAK,YAAY;AAGjB,SAAK,eAAe;AACpB,SAAK,cAAc;AAGnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AAInB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AAGvB,SAAK,OAAO,EAAE,MAAM,aAAa,IAAI,WAAW,OAAO,cAAc,QAAQ,YAAY;AAGzF,SAAK,eAAe,EAAE,MAAM,MAAM,QAAQ,QAAQ,MAAM,OAAO,OAAO,MAAM,IAAI;AAGhF,SAAK,UAAU,EAAE,KAAK,MAAM,QAAQ,KAAK,MAAM,UAAU;AAGzD,SAAK,UAAU,KAAK,OAAO,MAAM;AACjC,SAAK,YAAY,KAAK,OAAO,SAAS,MAAM;AAC5C,SAAK,QAAQ,KAAK,OAAO;AAGzB,SAAK,uBAAuB;AAM5B,SAAK,gBAAgB,WAAY;AAEhC,aAAO,UAAU;AAAA,IAElB;AAEA,SAAK,oBAAoB,WAAY;AAEpC,aAAO,UAAU;AAAA,IAElB;AAEA,SAAK,cAAc,WAAY;AAE9B,aAAO,KAAK,OAAO,SAAS,WAAY,KAAK,MAAO;AAAA,IAErD;AAEA,SAAK,oBAAoB,SAAWA,aAAa;AAEhD,MAAAA,YAAW,iBAAkB,WAAW,SAAU;AAClD,WAAK,uBAAuBA;AAAA,IAE7B;AAEA,SAAK,wBAAwB,WAAY;AAExC,WAAK,qBAAqB,oBAAqB,WAAW,SAAU;AACpE,WAAK,uBAAuB;AAAA,IAE7B;AAEA,SAAK,YAAY,WAAY;AAE5B,YAAM,QAAQ,KAAM,MAAM,MAAO;AACjC,YAAM,UAAU,KAAM,MAAM,OAAO,QAAS;AAC5C,YAAM,QAAQ,MAAM,OAAO;AAAA,IAE5B;AAEA,SAAK,QAAQ,WAAY;AAExB,YAAM,OAAO,KAAM,MAAM,OAAQ;AACjC,YAAM,OAAO,SAAS,KAAM,MAAM,SAAU;AAC5C,YAAM,OAAO,OAAO,MAAM;AAE1B,YAAM,OAAO,uBAAuB;AACpC,YAAM,cAAe,YAAa;AAElC,YAAM,OAAO;AAEb,cAAQ,MAAM;AAAA,IAEf;AAGA,SAAK,SAAS,WAAY;AAEzB,YAAM,SAAS,IAAI,QAAQ;AAG3B,YAAM,OAAO,IAAI,WAAW,EAAE,mBAAoB,OAAO,IAAI,IAAI,QAAS,GAAG,GAAG,CAAE,CAAE;AACpF,YAAM,cAAc,KAAK,MAAM,EAAE,OAAO;AAExC,YAAM,eAAe,IAAI,QAAQ;AACjC,YAAM,iBAAiB,IAAI,WAAW;AAEtC,YAAM,QAAQ,IAAI,KAAK;AAEvB,aAAO,SAAS,SAAS;AAExB,cAAM,WAAW,MAAM,OAAO;AAE9B,eAAO,KAAM,QAAS,EAAE,IAAK,MAAM,MAAO;AAG1C,eAAO,gBAAiB,IAAK;AAG7B,kBAAU,eAAgB,MAAO;AAEjC,YAAK,MAAM,cAAc,UAAU,MAAM,MAAO;AAE/C,qBAAY,qBAAqB,CAAE;AAAA,QAEpC;AAEA,YAAK,MAAM,eAAgB;AAE1B,oBAAU,SAAS,eAAe,QAAQ,MAAM;AAChD,oBAAU,OAAO,eAAe,MAAM,MAAM;AAAA,QAE7C,OAAO;AAEN,oBAAU,SAAS,eAAe;AAClC,oBAAU,OAAO,eAAe;AAAA,QAEjC;AAIA,YAAI,MAAM,MAAM;AAChB,YAAI,MAAM,MAAM;AAEhB,YAAK,SAAU,GAAI,KAAK,SAAU,GAAI,GAAI;AAEzC,cAAK,MAAM,CAAE,KAAK;AAAK,mBAAO;AAAA,mBAAiB,MAAM,KAAK;AAAK,mBAAO;AAEtE,cAAK,MAAM,CAAE,KAAK;AAAK,mBAAO;AAAA,mBAAiB,MAAM,KAAK;AAAK,mBAAO;AAEtE,cAAK,OAAO,KAAM;AAEjB,sBAAU,QAAQ,KAAK,IAAK,KAAK,KAAK,IAAK,KAAK,UAAU,KAAM,CAAE;AAAA,UAEnE,OAAO;AAEN,sBAAU,QAAU,UAAU,SAAU,MAAM,OAAQ,IACrD,KAAK,IAAK,KAAK,UAAU,KAAM,IAC/B,KAAK,IAAK,KAAK,UAAU,KAAM;AAAA,UAEjC;AAAA,QAED;AAGA,kBAAU,MAAM,KAAK,IAAK,MAAM,eAAe,KAAK,IAAK,MAAM,eAAe,UAAU,GAAI,CAAE;AAE9F,kBAAU,SAAS;AAGnB,kBAAU,UAAU;AAGpB,kBAAU,SAAS,KAAK,IAAK,MAAM,aAAa,KAAK,IAAK,MAAM,aAAa,UAAU,MAAO,CAAE;AAIhG,YAAK,MAAM,kBAAkB,MAAO;AAEnC,gBAAM,OAAO,gBAAiB,WAAW,MAAM,aAAc;AAAA,QAE9D,OAAO;AAEN,gBAAM,OAAO,IAAK,SAAU;AAAA,QAE7B;AAEA,eAAO,iBAAkB,SAAU;AAGnC,eAAO,gBAAiB,WAAY;AAEpC,iBAAS,KAAM,MAAM,MAAO,EAAE,IAAK,MAAO;AAE1C,cAAM,OAAO,OAAQ,MAAM,MAAO;AAElC,YAAK,MAAM,kBAAkB,MAAO;AAEnC,yBAAe,SAAW,IAAI,MAAM;AACpC,yBAAe,OAAS,IAAI,MAAM;AAElC,oBAAU,eAAgB,IAAI,MAAM,aAAc;AAAA,QAEnD,OAAO;AAEN,yBAAe,IAAK,GAAG,GAAG,CAAE;AAE5B,oBAAU,IAAK,GAAG,GAAG,CAAE;AAAA,QAExB;AAEA,gBAAQ;AAMR,YAAK,eACJ,aAAa,kBAAmB,MAAM,OAAO,QAAS,IAAI,OAC1D,KAAM,IAAI,eAAe,IAAK,MAAM,OAAO,UAAW,KAAM,KAAM;AAElE,gBAAM,cAAe,YAAa;AAElC,uBAAa,KAAM,MAAM,OAAO,QAAS;AACzC,yBAAe,KAAM,MAAM,OAAO,UAAW;AAC7C,wBAAc;AAEd,iBAAO;AAAA,QAER;AAEA,eAAO;AAAA,MAER;AAAA,IAED,EAAE;AAEF,SAAK,UAAU,WAAY;AAE1B,YAAM,WAAW,oBAAqB,eAAe,aAAc;AAEnE,YAAM,WAAW,oBAAqB,eAAe,aAAc;AACnE,YAAM,WAAW,oBAAqB,iBAAiB,WAAY;AACnE,YAAM,WAAW,oBAAqB,SAAS,YAAa;AAE5D,YAAM,WAAW,oBAAqB,eAAe,aAAc;AACnE,YAAM,WAAW,oBAAqB,aAAa,WAAY;AAG/D,UAAK,MAAM,yBAAyB,MAAO;AAE1C,cAAM,qBAAqB,oBAAqB,WAAW,SAAU;AACrE,cAAM,uBAAuB;AAAA,MAE9B;AAAA,IAID;AAMA,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAAA,MACb,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,cAAc;AAAA,MACd,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,IACrB;AAEA,QAAI,QAAQ,MAAM;AAElB,UAAM,MAAM;AAGZ,UAAM,YAAY,IAAI,UAAU;AAChC,UAAM,iBAAiB,IAAI,UAAU;AAErC,QAAI,QAAQ;AACZ,UAAM,YAAY,IAAI,QAAQ;AAC9B,QAAI,cAAc;AAElB,UAAM,cAAc,IAAI,QAAQ;AAChC,UAAM,YAAY,IAAI,QAAQ;AAC9B,UAAM,cAAc,IAAI,QAAQ;AAEhC,UAAM,WAAW,IAAI,QAAQ;AAC7B,UAAM,SAAS,IAAI,QAAQ;AAC3B,UAAM,WAAW,IAAI,QAAQ;AAE7B,UAAM,aAAa,IAAI,QAAQ;AAC/B,UAAM,WAAW,IAAI,QAAQ;AAC7B,UAAM,aAAa,IAAI,QAAQ;AAE/B,UAAM,WAAW,CAAC;AAClB,UAAM,mBAAmB,CAAC;AAE1B,aAAS,uBAAuB;AAE/B,aAAO,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM;AAAA,IAEtC;AAEA,aAAS,eAAe;AAEvB,aAAO,KAAK,IAAK,MAAM,MAAM,SAAU;AAAA,IAExC;AAEA,aAAS,WAAY,OAAQ;AAE5B,qBAAe,SAAS;AAAA,IAEzB;AAEA,aAAS,SAAU,OAAQ;AAE1B,qBAAe,OAAO;AAAA,IAEvB;AAEA,UAAM,UAAU,WAAY;AAE3B,YAAM,IAAI,IAAI,QAAQ;AAEtB,aAAO,SAASC,SAAS,UAAU,cAAe;AAEjD,UAAE,oBAAqB,cAAc,CAAE;AACvC,UAAE,eAAgB,CAAE,QAAS;AAE7B,kBAAU,IAAK,CAAE;AAAA,MAElB;AAAA,IAED,EAAE;AAEF,UAAM,QAAQ,WAAY;AAEzB,YAAM,IAAI,IAAI,QAAQ;AAEtB,aAAO,SAASC,OAAO,UAAU,cAAe;AAE/C,YAAK,MAAM,uBAAuB,MAAO;AAExC,YAAE,oBAAqB,cAAc,CAAE;AAAA,QAExC,OAAO;AAEN,YAAE,oBAAqB,cAAc,CAAE;AACvC,YAAE,aAAc,MAAM,OAAO,IAAI,CAAE;AAAA,QAEpC;AAEA,UAAE,eAAgB,QAAS;AAE3B,kBAAU,IAAK,CAAE;AAAA,MAElB;AAAA,IAED,EAAE;AAGF,UAAM,MAAM,WAAY;AAEvB,YAAM,SAAS,IAAI,QAAQ;AAE3B,aAAO,SAASC,KAAK,QAAQ,QAAS;AAErC,cAAM,UAAU,MAAM;AAEtB,YAAK,MAAM,OAAO,qBAAsB;AAGvC,gBAAM,WAAW,MAAM,OAAO;AAC9B,iBAAO,KAAM,QAAS,EAAE,IAAK,MAAM,MAAO;AAC1C,cAAI,iBAAiB,OAAO,OAAO;AAGnC,4BAAkB,KAAK,IAAO,MAAM,OAAO,MAAM,IAAM,KAAK,KAAK,GAAM;AAGvE,kBAAS,IAAI,SAAS,iBAAiB,QAAQ,cAAc,MAAM,OAAO,MAAO;AACjF,gBAAO,IAAI,SAAS,iBAAiB,QAAQ,cAAc,MAAM,OAAO,MAAO;AAAA,QAEhF,WAAY,MAAM,OAAO,sBAAuB;AAG/C,kBAAS,UAAW,MAAM,OAAO,QAAQ,MAAM,OAAO,QAAS,MAAM,OAAO,OAAO,QAAQ,aAAa,MAAM,OAAO,MAAO;AAC5H,gBAAO,UAAW,MAAM,OAAO,MAAM,MAAM,OAAO,UAAW,MAAM,OAAO,OAAO,QAAQ,cAAc,MAAM,OAAO,MAAO;AAAA,QAE5H,OAAO;AAGN,kBAAQ,KAAM,8EAA+E;AAC7F,gBAAM,YAAY;AAAA,QAEnB;AAAA,MAED;AAAA,IAED,EAAE;AAEF,aAAS,SAAU,YAAa;AAE/B,UAAK,MAAM,OAAO,qBAAsB;AAEvC,iBAAS;AAAA,MAEV,WAAY,MAAM,OAAO,sBAAuB;AAE/C,cAAM,OAAO,OAAO,KAAK,IAAK,MAAM,SAAS,KAAK,IAAK,MAAM,SAAS,MAAM,OAAO,OAAO,UAAW,CAAE;AACvG,cAAM,OAAO,uBAAuB;AACpC,sBAAc;AAAA,MAEf,OAAO;AAEN,gBAAQ,KAAM,qFAAsF;AACpG,cAAM,aAAa;AAAA,MAEpB;AAAA,IAED;AAEA,aAAS,QAAS,YAAa;AAE9B,UAAK,MAAM,OAAO,qBAAsB;AAEvC,iBAAS;AAAA,MAEV,WAAY,MAAM,OAAO,sBAAuB;AAE/C,cAAM,OAAO,OAAO,KAAK,IAAK,MAAM,SAAS,KAAK,IAAK,MAAM,SAAS,MAAM,OAAO,OAAO,UAAW,CAAE;AACvG,cAAM,OAAO,uBAAuB;AACpC,sBAAc;AAAA,MAEf,OAAO;AAEN,gBAAQ,KAAM,qFAAsF;AACpG,cAAM,aAAa;AAAA,MAEpB;AAAA,IAED;AAMA,aAAS,sBAAuB,OAAQ;AAEvC,kBAAY,IAAK,MAAM,SAAS,MAAM,OAAQ;AAAA,IAE/C;AAEA,aAAS,qBAAsB,OAAQ;AAEtC,iBAAW,IAAK,MAAM,SAAS,MAAM,OAAQ;AAAA,IAE9C;AAEA,aAAS,mBAAoB,OAAQ;AAEpC,eAAS,IAAK,MAAM,SAAS,MAAM,OAAQ;AAAA,IAE5C;AAEA,aAAS,sBAAuB,OAAQ;AAEvC,gBAAU,IAAK,MAAM,SAAS,MAAM,OAAQ;AAE5C,kBAAY,WAAY,WAAW,WAAY,EAAE,eAAgB,MAAM,WAAY;AAEnF,YAAM,UAAU,MAAM;AAEtB,iBAAY,IAAI,KAAK,KAAK,YAAY,IAAI,QAAQ,YAAa;AAE/D,eAAU,IAAI,KAAK,KAAK,YAAY,IAAI,QAAQ,YAAa;AAE7D,kBAAY,KAAM,SAAU;AAE5B,YAAM,OAAO;AAAA,IAEd;AAEA,aAAS,qBAAsB,OAAQ;AAEtC,eAAS,IAAK,MAAM,SAAS,MAAM,OAAQ;AAE3C,iBAAW,WAAY,UAAU,UAAW;AAE5C,UAAK,WAAW,IAAI,GAAI;AAEvB,iBAAU,aAAa,CAAE;AAAA,MAE1B,WAAY,WAAW,IAAI,GAAI;AAE9B,gBAAS,aAAa,CAAE;AAAA,MAEzB;AAEA,iBAAW,KAAM,QAAS;AAE1B,YAAM,OAAO;AAAA,IAEd;AAEA,aAAS,mBAAoB,OAAQ;AAEpC,aAAO,IAAK,MAAM,SAAS,MAAM,OAAQ;AAEzC,eAAS,WAAY,QAAQ,QAAS,EAAE,eAAgB,MAAM,QAAS;AAEvE,UAAK,SAAS,GAAG,SAAS,CAAE;AAE5B,eAAS,KAAM,MAAO;AAEtB,YAAM,OAAO;AAAA,IAEd;AAEA,aAAS,iBAAkB,OAAQ;AAElC,UAAK,MAAM,SAAS,GAAI;AAEvB,gBAAS,aAAa,CAAE;AAAA,MAEzB,WAAY,MAAM,SAAS,GAAI;AAE9B,iBAAU,aAAa,CAAE;AAAA,MAE1B;AAEA,YAAM,OAAO;AAAA,IAEd;AAEA,aAAS,cAAe,OAAQ;AAE/B,UAAI,cAAc;AAElB,cAAS,MAAM,MAAO;AAAA,QAErB,KAAK,MAAM,KAAK;AAEf,cAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,qBAAU,IAAI,KAAK,KAAK,MAAM,cAAc,MAAM,WAAW,YAAa;AAAA,UAE3E,OAAO;AAEN,gBAAK,GAAG,MAAM,WAAY;AAAA,UAE3B;AAEA,wBAAc;AACd;AAAA,QAED,KAAK,MAAM,KAAK;AAEf,cAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,qBAAU,KAAM,KAAK,KAAK,MAAM,cAAc,MAAM,WAAW,YAAa;AAAA,UAE7E,OAAO;AAEN,gBAAK,GAAG,CAAE,MAAM,WAAY;AAAA,UAE7B;AAEA,wBAAc;AACd;AAAA,QAED,KAAK,MAAM,KAAK;AAEf,cAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,uBAAY,IAAI,KAAK,KAAK,MAAM,cAAc,MAAM,WAAW,YAAa;AAAA,UAE7E,OAAO;AAEN,gBAAK,MAAM,aAAa,CAAE;AAAA,UAE3B;AAEA,wBAAc;AACd;AAAA,QAED,KAAK,MAAM,KAAK;AAEf,cAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,uBAAY,KAAM,KAAK,KAAK,MAAM,cAAc,MAAM,WAAW,YAAa;AAAA,UAE/E,OAAO;AAEN,gBAAK,CAAE,MAAM,aAAa,CAAE;AAAA,UAE7B;AAEA,wBAAc;AACd;AAAA,MAEF;AAEA,UAAK,aAAc;AAGlB,cAAM,eAAe;AAErB,cAAM,OAAO;AAAA,MAEd;AAAA,IAGD;AAEA,aAAS,yBAAyB;AAEjC,UAAK,SAAS,WAAW,GAAI;AAE5B,oBAAY,IAAK,SAAU,CAAE,EAAE,OAAO,SAAU,CAAE,EAAE,KAAM;AAAA,MAE3D,OAAO;AAEN,cAAM,IAAI,OAAQ,SAAU,CAAE,EAAE,QAAQ,SAAU,CAAE,EAAE;AACtD,cAAM,IAAI,OAAQ,SAAU,CAAE,EAAE,QAAQ,SAAU,CAAE,EAAE;AAEtD,oBAAY,IAAK,GAAG,CAAE;AAAA,MAEvB;AAAA,IAED;AAEA,aAAS,sBAAsB;AAE9B,UAAK,SAAS,WAAW,GAAI;AAE5B,iBAAS,IAAK,SAAU,CAAE,EAAE,OAAO,SAAU,CAAE,EAAE,KAAM;AAAA,MAExD,OAAO;AAEN,cAAM,IAAI,OAAQ,SAAU,CAAE,EAAE,QAAQ,SAAU,CAAE,EAAE;AACtD,cAAM,IAAI,OAAQ,SAAU,CAAE,EAAE,QAAQ,SAAU,CAAE,EAAE;AAEtD,iBAAS,IAAK,GAAG,CAAE;AAAA,MAEpB;AAAA,IAED;AAEA,aAAS,wBAAwB;AAEhC,YAAM,KAAK,SAAU,CAAE,EAAE,QAAQ,SAAU,CAAE,EAAE;AAC/C,YAAM,KAAK,SAAU,CAAE,EAAE,QAAQ,SAAU,CAAE,EAAE;AAE/C,YAAM,WAAW,KAAK,KAAM,KAAK,KAAK,KAAK,EAAG;AAE9C,iBAAW,IAAK,GAAG,QAAS;AAAA,IAE7B;AAEA,aAAS,2BAA2B;AAEnC,UAAK,MAAM;AAAa,8BAAsB;AAE9C,UAAK,MAAM;AAAY,4BAAoB;AAAA,IAE5C;AAEA,aAAS,8BAA8B;AAEtC,UAAK,MAAM;AAAa,8BAAsB;AAE9C,UAAK,MAAM;AAAe,+BAAuB;AAAA,IAElD;AAEA,aAAS,sBAAuB,OAAQ;AAEvC,UAAK,SAAS,UAAU,GAAI;AAE3B,kBAAU,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,MAEzC,OAAO;AAEN,cAAM,WAAW,yBAA0B,KAAM;AAEjD,cAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AACzC,cAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AAEzC,kBAAU,IAAK,GAAG,CAAE;AAAA,MAErB;AAEA,kBAAY,WAAY,WAAW,WAAY,EAAE,eAAgB,MAAM,WAAY;AAEnF,YAAM,UAAU,MAAM;AAEtB,iBAAY,IAAI,KAAK,KAAK,YAAY,IAAI,QAAQ,YAAa;AAE/D,eAAU,IAAI,KAAK,KAAK,YAAY,IAAI,QAAQ,YAAa;AAE7D,kBAAY,KAAM,SAAU;AAAA,IAE7B;AAEA,aAAS,mBAAoB,OAAQ;AAEpC,UAAK,SAAS,WAAW,GAAI;AAE5B,eAAO,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,MAEtC,OAAO;AAEN,cAAM,WAAW,yBAA0B,KAAM;AAEjD,cAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AACzC,cAAM,IAAI,OAAQ,MAAM,QAAQ,SAAS;AAEzC,eAAO,IAAK,GAAG,CAAE;AAAA,MAElB;AAEA,eAAS,WAAY,QAAQ,QAAS,EAAE,eAAgB,MAAM,QAAS;AAEvE,UAAK,SAAS,GAAG,SAAS,CAAE;AAE5B,eAAS,KAAM,MAAO;AAAA,IAEvB;AAEA,aAAS,qBAAsB,OAAQ;AAEtC,YAAM,WAAW,yBAA0B,KAAM;AAEjD,YAAM,KAAK,MAAM,QAAQ,SAAS;AAClC,YAAM,KAAK,MAAM,QAAQ,SAAS;AAElC,YAAM,WAAW,KAAK,KAAM,KAAK,KAAK,KAAK,EAAG;AAE9C,eAAS,IAAK,GAAG,QAAS;AAE1B,iBAAW,IAAK,GAAG,KAAK,IAAK,SAAS,IAAI,WAAW,GAAG,MAAM,SAAU,CAAE;AAE1E,eAAU,WAAW,CAAE;AAEvB,iBAAW,KAAM,QAAS;AAAA,IAE3B;AAEA,aAAS,wBAAyB,OAAQ;AAEzC,UAAK,MAAM;AAAa,6BAAsB,KAAM;AAEpD,UAAK,MAAM;AAAY,2BAAoB,KAAM;AAAA,IAElD;AAEA,aAAS,2BAA4B,OAAQ;AAE5C,UAAK,MAAM;AAAa,6BAAsB,KAAM;AAEpD,UAAK,MAAM;AAAe,8BAAuB,KAAM;AAAA,IAExD;AAMA,aAAS,cAAe,OAAQ;AAE/B,UAAK,MAAM,YAAY;AAAQ;AAE/B,UAAK,SAAS,WAAW,GAAI;AAE5B,cAAM,WAAW,kBAAmB,MAAM,SAAU;AAEpD,cAAM,WAAW,iBAAkB,eAAe,aAAc;AAChE,cAAM,WAAW,iBAAkB,aAAa,WAAY;AAAA,MAE7D;AAIA,iBAAY,KAAM;AAElB,UAAK,MAAM,gBAAgB,SAAU;AAEpC,qBAAc,KAAM;AAAA,MAErB,OAAO;AAEN,oBAAa,KAAM;AAAA,MAEpB;AAAA,IAED;AAEA,aAAS,cAAe,OAAQ;AAE/B,UAAK,MAAM,YAAY;AAAQ;AAE/B,UAAK,MAAM,gBAAgB,SAAU;AAEpC,oBAAa,KAAM;AAAA,MAEpB,OAAO;AAEN,oBAAa,KAAM;AAAA,MAEpB;AAAA,IAED;AAEA,aAAS,YAAa,OAAQ;AAE7B,oBAAe,KAAM;AAErB,UAAK,SAAS,WAAW,GAAI;AAE5B,cAAM,WAAW,sBAAuB,MAAM,SAAU;AAExD,cAAM,WAAW,oBAAqB,eAAe,aAAc;AACnE,cAAM,WAAW,oBAAqB,aAAa,WAAY;AAAA,MAEhE;AAEA,YAAM,cAAe,SAAU;AAE/B,cAAQ,MAAM;AAAA,IAEf;AAEA,aAAS,YAAa,OAAQ;AAE7B,UAAI;AAEJ,cAAS,MAAM,QAAS;AAAA,QAEvB,KAAK;AAEJ,wBAAc,MAAM,aAAa;AACjC;AAAA,QAED,KAAK;AAEJ,wBAAc,MAAM,aAAa;AACjC;AAAA,QAED,KAAK;AAEJ,wBAAc,MAAM,aAAa;AACjC;AAAA,QAED;AAEC,wBAAc;AAAA,MAEhB;AAEA,cAAS,aAAc;AAAA,QAEtB,KAAK,MAAM;AAEV,cAAK,MAAM,eAAe;AAAQ;AAElC,+BAAsB,KAAM;AAE5B,kBAAQ,MAAM;AAEd;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,gBAAK,MAAM,cAAc;AAAQ;AAEjC,+BAAoB,KAAM;AAE1B,oBAAQ,MAAM;AAAA,UAEf,OAAO;AAEN,gBAAK,MAAM,iBAAiB;AAAQ;AAEpC,kCAAuB,KAAM;AAE7B,oBAAQ,MAAM;AAAA,UAEf;AAEA;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,WAAW,MAAM,WAAW,MAAM,UAAW;AAEvD,gBAAK,MAAM,iBAAiB;AAAQ;AAEpC,kCAAuB,KAAM;AAE7B,oBAAQ,MAAM;AAAA,UAEf,OAAO;AAEN,gBAAK,MAAM,cAAc;AAAQ;AAEjC,+BAAoB,KAAM;AAE1B,oBAAQ,MAAM;AAAA,UAEf;AAEA;AAAA,QAED;AAEC,kBAAQ,MAAM;AAAA,MAEhB;AAEA,UAAK,UAAU,MAAM,MAAO;AAE3B,cAAM,cAAe,WAAY;AAAA,MAElC;AAAA,IAED;AAEA,aAAS,YAAa,OAAQ;AAE7B,cAAS,OAAQ;AAAA,QAEhB,KAAK,MAAM;AAEV,cAAK,MAAM,iBAAiB;AAAQ;AAEpC,gCAAuB,KAAM;AAE7B;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,eAAe;AAAQ;AAElC,+BAAsB,KAAM;AAE5B;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,cAAc;AAAQ;AAEjC,6BAAoB,KAAM;AAE1B;AAAA,MAEF;AAAA,IAED;AAEA,aAAS,aAAc,OAAQ;AAE9B,UAAK,MAAM,YAAY,SAAS,MAAM,eAAe,SAAS,UAAU,MAAM;AAAO;AAErF,YAAM,eAAe;AAErB,YAAM,cAAe,WAAY;AAEjC,uBAAkB,KAAM;AAExB,YAAM,cAAe,SAAU;AAAA,IAEhC;AAEA,aAAS,UAAW,OAAQ;AAE3B,UAAK,MAAM,YAAY,SAAS,MAAM,cAAc;AAAQ;AAE5D,oBAAe,KAAM;AAAA,IAEtB;AAEA,aAAS,aAAc,OAAQ;AAE9B,mBAAc,KAAM;AAEpB,cAAS,SAAS,QAAS;AAAA,QAE1B,KAAK;AAEJ,kBAAS,MAAM,QAAQ,KAAM;AAAA,YAE5B,KAAK,MAAM;AAEV,kBAAK,MAAM,iBAAiB;AAAQ;AAEpC,qCAAuB;AAEvB,sBAAQ,MAAM;AAEd;AAAA,YAED,KAAK,MAAM;AAEV,kBAAK,MAAM,cAAc;AAAQ;AAEjC,kCAAoB;AAEpB,sBAAQ,MAAM;AAEd;AAAA,YAED;AAEC,sBAAQ,MAAM;AAAA,UAEhB;AAEA;AAAA,QAED,KAAK;AAEJ,kBAAS,MAAM,QAAQ,KAAM;AAAA,YAE5B,KAAK,MAAM;AAEV,kBAAK,MAAM,eAAe,SAAS,MAAM,cAAc;AAAQ;AAE/D,uCAAyB;AAEzB,sBAAQ,MAAM;AAEd;AAAA,YAED,KAAK,MAAM;AAEV,kBAAK,MAAM,eAAe,SAAS,MAAM,iBAAiB;AAAQ;AAElE,0CAA4B;AAE5B,sBAAQ,MAAM;AAEd;AAAA,YAED;AAEC,sBAAQ,MAAM;AAAA,UAEhB;AAEA;AAAA,QAED;AAEC,kBAAQ,MAAM;AAAA,MAEhB;AAEA,UAAK,UAAU,MAAM,MAAO;AAE3B,cAAM,cAAe,WAAY;AAAA,MAElC;AAAA,IAED;AAEA,aAAS,YAAa,OAAQ;AAE7B,mBAAc,KAAM;AAEpB,cAAS,OAAQ;AAAA,QAEhB,KAAK,MAAM;AAEV,cAAK,MAAM,iBAAiB;AAAQ;AAEpC,gCAAuB,KAAM;AAE7B,gBAAM,OAAO;AAEb;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,cAAc;AAAQ;AAEjC,6BAAoB,KAAM;AAE1B,gBAAM,OAAO;AAEb;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,eAAe,SAAS,MAAM,cAAc;AAAQ;AAE/D,kCAAyB,KAAM;AAE/B,gBAAM,OAAO;AAEb;AAAA,QAED,KAAK,MAAM;AAEV,cAAK,MAAM,eAAe,SAAS,MAAM,iBAAiB;AAAQ;AAElE,qCAA4B,KAAM;AAElC,gBAAM,OAAO;AAEb;AAAA,QAED;AAEC,kBAAQ,MAAM;AAAA,MAEhB;AAAA,IAED;AAEA,aAAS,cAAe,OAAQ;AAE/B,UAAK,MAAM,YAAY;AAAQ;AAE/B,YAAM,eAAe;AAAA,IAEtB;AAEA,aAAS,WAAY,OAAQ;AAE5B,eAAS,KAAM,KAAM;AAAA,IAEtB;AAEA,aAAS,cAAe,OAAQ;AAE/B,aAAO,iBAAkB,MAAM,SAAU;AAEzC,eAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAO;AAE5C,YAAK,SAAU,CAAE,EAAE,aAAa,MAAM,WAAY;AAEjD,mBAAS,OAAQ,GAAG,CAAE;AACtB;AAAA,QAED;AAAA,MAED;AAAA,IAED;AAEA,aAAS,aAAc,OAAQ;AAE9B,UAAI,WAAW,iBAAkB,MAAM,SAAU;AAEjD,UAAK,aAAa,QAAY;AAE7B,mBAAW,IAAI,QAAQ;AACvB,yBAAkB,MAAM,SAAU,IAAI;AAAA,MAEvC;AAEA,eAAS,IAAK,MAAM,OAAO,MAAM,KAAM;AAAA,IAExC;AAEA,aAAS,yBAA0B,OAAQ;AAE1C,YAAM,UAAY,MAAM,cAAc,SAAU,CAAE,EAAE,YAAc,SAAU,CAAE,IAAI,SAAU,CAAE;AAE9F,aAAO,iBAAkB,QAAQ,SAAU;AAAA,IAE5C;AAIA,UAAM,WAAW,iBAAkB,eAAe,aAAc;AAEhE,UAAM,WAAW,iBAAkB,eAAe,aAAc;AAChE,UAAM,WAAW,iBAAkB,iBAAiB,WAAY;AAChE,UAAM,WAAW,iBAAkB,SAAS,cAAc,EAAE,SAAS,MAAM,CAAE;AAI7E,SAAK,OAAO;AAAA,EAEb;AAED;", "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"]}